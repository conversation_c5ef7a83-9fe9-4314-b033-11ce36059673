from Frontend.GUI import(
GraphicalUserInterface,
SetAssistantStatus,
ShowTextToScreen,
TempDirectoryPath,
SetMicrophoneStatus,
AnswerModifier,
QueryModifier,
GetMicrophoneStatus,
GetAssistantStatus)
from Backend.Model import FirstLayerDMM
from Backend.RealtimeSearchEngine import RealtimeSearchEngine
from Backend.Automation import Automation
from Backend.SpeechToText import SpeechRecognition
from Backend.Chatbot import ChatBot
from Backend.TextToSpeech import text_to_speech, cleanup_tts
from Backend.DateTime import process_datetime_query
from Backend.TodoManager import process_todo_command

# Import emotion analysis
try:
    from Backend.EmotionManager import get_current_emotion, get_emotion_context, get_emotion_summary
    from Backend.EmotionBridge import start_emotion_bridge, stop_emotion_bridge, get_emotion_bridge_status
    EMOTION_ANALYSIS_AVAILABLE = True
    print("✅ Emotion analysis integration loaded")
except ImportError as e:
    print(f"⚠️ Emotion analysis not available: {e}")
    EMOTION_ANALYSIS_AVAILABLE = False
from dotenv import dotenv_values
from asyncio import run
from time import sleep
import subprocess
import threading
import json
import os
import sys
import tempfile
import atexit
import time

# Updated: Ensure TempDirectoryPath is callable or a string
def get_temp_directory_path(filename):
    if callable(TempDirectoryPath):
        return TempDirectoryPath(filename)
    elif isinstance(TempDirectoryPath, str):
        return os.path.join(TempDirectoryPath, filename)
    else:
        raise TypeError("TempDirectoryPath is neither callable nor a string. Check its definition in Frontend/GUI.py")

def enhance_response_with_emotion(response_text):
    """Enhance AI response based on detected user emotion."""
    if not EMOTION_ANALYSIS_AVAILABLE:
        return response_text

    try:
        emotion_context = get_emotion_context()
        current_emotion = emotion_context.get("emotion", "neutral")
        confidence = emotion_context.get("confidence", 0.0)

        # Only enhance if confidence is reasonably high
        if confidence < 0.6:
            return response_text

        # Add emotion-aware prefixes or modifications
        emotion_enhancements = {
            "happy": "I can sense your positive energy! ",
            "excited": "Your enthusiasm is contagious! ",
            "sad": "I understand you might be feeling down. ",
            "angry": "I can hear the frustration in your voice. ",
            "calm": "I appreciate your calm demeanor. ",
            "neutral": ""
        }

        enhancement = emotion_enhancements.get(current_emotion, "")

        # Add emotion context to response if enhancement exists
        if enhancement and not response_text.startswith(enhancement):
            return enhancement + response_text

        return response_text

    except Exception as e:
        print(f"Error enhancing response with emotion: {e}")
        return response_text

def log_emotion_interaction(query, response, emotion_data):
    """Log the interaction with emotion context for analysis."""
    if not EMOTION_ANALYSIS_AVAILABLE:
        return

    try:
        interaction_log = {
            "timestamp": time.time(),
            "user_query": query,
            "ai_response": response,
            "emotion": emotion_data.get("emotion", "neutral"),
            "confidence": emotion_data.get("confidence", 0.0)
        }

        # Save to interaction log file
        log_file = os.path.join("Data", "EmotionInteractionLog.json")

        # Load existing log or create new
        if os.path.exists(log_file):
            with open(log_file, "r", encoding="utf-8") as f:
                log_data = json.load(f)
        else:
            log_data = {"interactions": []}

        # Add new interaction
        log_data["interactions"].append(interaction_log)

        # Keep only last 100 interactions
        if len(log_data["interactions"]) > 100:
            log_data["interactions"] = log_data["interactions"][-100:]

        # Save updated log
        with open(log_file, "w", encoding="utf-8") as f:
            json.dump(log_data, f, indent=2)

    except Exception as e:
        print(f"Error logging emotion interaction: {e}")

env_vars = dotenv_values(".env")
Username = env_vars.get("Username")
Assistantname = env_vars.get("Assistantname")
DefaultMessage = f'''{Username} : Hello {Assistantname}, How are you?
{Assistantname} : Welcome {Username}. I am doing well. How may i help you?'''
subprocess_list = []
Functions = ["open", "close", "play", "system", "google search", "youtube search", "url", "datetime", "send email", "todo add", "todo list", "todo remove", "todo edit", "todo complete", "todo show"]

# Update path handling for cross-platform compatibility
def ShowDefaultChatIfNoChats():
    chat_path = os.path.join('Data', 'ChatLog.json')
    if not os.path.exists(chat_path) or os.path.getsize(chat_path) < 5:
        # Improved file handling with context managers
        with open(get_temp_directory_path('Database.data'), 'w', encoding='utf-8') as file:
            file.write("")

        with open(get_temp_directory_path('Responses.data'), 'w', encoding='utf-8') as file:
            file.write(DefaultMessage)

def ReadChatLogJson():
    chat_path = os.path.join('Data', 'ChatLog.json')
    with open(chat_path, 'r', encoding='utf-8') as file:
        chatlog_data = json.load(file)
    return chatlog_data

def ChatLogIntegration():
    json_data = ReadChatLogJson()
    formatted_chatlog = ""
    for entry in json_data:
        if entry["role"] == "user":
            formatted_chatlog += f"User{entry['content']}\n"
        elif entry["role"] == "assistant":
            formatted_chatlog += f"Assistant: {entry['content']}\n"
    formatted_chatlog = formatted_chatlog.replace("User", Username + " ")
    formatted_chatlog = formatted_chatlog.replace("Assistant", Assistantname + " ")

    with open(get_temp_directory_path('Database.data'), 'w', encoding='utf-8') as file:
        file.write(AnswerModifier(formatted_chatlog))

def ShowChatOnGUI():
    File = open(get_temp_directory_path('Database.data'), "r", encoding='utf-8')
    Data = File.read()
    if len(str(Data)) > 0:
        lines = Data.split('\n')
        result = '\n'.join(lines)
        File.close()
        File = open(get_temp_directory_path('Responses.data'), "w", encoding='utf-8')
        File.write(result)
        File.close()

def InitialSpeechSetup():
    """Initialize speech recognition system - microphone starts MUTED by default"""
    print("🎤 PRIORITY: Starting speech recognition system...")
    print("⚡ This happens BEFORE any GUI, animation, or other processes!")

    # Set microphone to MUTED/DISABLED by default - user must manually activate
    SetMicrophoneStatus("False")
    SetAssistantStatus("Initializing Speech Recognition...")
    print("� Microphone status set to MUTED (default)")

    # Initialize basic chat data
    ShowTextToScreen("")
    ShowDefaultChatIfNoChats()
    ChatLogIntegration()
    ShowChatOnGUI()
    print("💾 Chat data initialized")

    print("✅ Speech recognition system initialized!")
    print("🎯 Click the microphone button to start listening!")
    SetAssistantStatus("Ready - Click microphone to activate")

    return True

def InitialExecution():
    """Run after speech recognition is already active"""
    print("🎬 Running initial execution (speech already active)...")

    # Speech is already active, just speak welcome message
    welcome_message = "Hello sir How can i help you today!"
    ShowTextToScreen(f"{Assistantname} : {welcome_message}")
    SetAssistantStatus("Speaking...")
    text_to_speech(welcome_message)
    SetAssistantStatus("Available.....")

    # Signal that startup is complete
    signal_startup_complete()

def signal_loading_screen_complete():
    """Signal that the loading screen has completed successfully"""
    try:
        with open(get_temp_directory_path('LoadingComplete.data'), 'w', encoding='utf-8') as file:
            file.write("True")
        print("✅ Loading screen completed - GUI can now be shown")
    except Exception as e:
        print(f"Error signaling loading complete: {e}")

def is_loading_screen_complete():
    """Check if the loading screen has completed"""
    try:
        loading_file = get_temp_directory_path('LoadingComplete.data')
        if os.path.exists(loading_file):
            with open(loading_file, 'r', encoding='utf-8') as file:
                return file.read().strip().lower() == "true"
    except Exception as e:
        print(f"Error checking loading status: {e}")
    return False

def signal_startup_complete():
    """Signal that the startup sequence is complete and GUI can be shown"""
    try:
        with open(get_temp_directory_path('StartupComplete.data'), 'w', encoding='utf-8') as file:
            file.write("True")
        print("✅ Startup sequence complete - GUI can now be shown")
    except Exception as e:
        print(f"Error signaling startup complete: {e}")

def is_startup_complete():
    """Check if the startup sequence is complete"""
    try:
        startup_file = get_temp_directory_path('StartupComplete.data')
        if os.path.exists(startup_file):
            with open(startup_file, 'r', encoding='utf-8') as file:
                return file.read().strip().lower() == "true"
    except Exception as e:
        print(f"Error checking startup status: {e}")
    return False

def is_gui_ready():
    """Check if the GUI is ready to be shown"""
    try:
        gui_ready_file = get_temp_directory_path('GUI_Ready.data')
        if os.path.exists(gui_ready_file):
            with open(gui_ready_file, 'r', encoding='utf-8') as file:
                return file.read().strip().lower() == "true"
    except Exception as e:
        print(f"Error checking GUI ready status: {e}")
    return False

def signal_gui_ready():
    """Signal that the GUI is ready but hidden"""
    try:
        with open(get_temp_directory_path('GUI_Ready.data'), 'w', encoding='utf-8') as file:
            file.write("True")
        print("✅ GUI ready signal sent")
    except Exception as e:
        print(f"Error signaling GUI ready: {e}")

def cleanup_startup_files():
    """Clean up all startup coordination files"""
    files_to_clean = [
        'LoadingComplete.data',
        'StartupComplete.data',
        'GUI_Ready.data'
    ]

    for filename in files_to_clean:
        try:
            file_path = get_temp_directory_path(filename)
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"🧹 Cleaned up {filename}")
        except Exception as e:
            print(f"⚠️ Error cleaning {filename}: {e}")

# Don't run InitialExecution immediately - it will be called after GUI is ready

def MainExecution():
        TaskExecution = False
        ImageExecution = False
        ImageGenerationQuery = ""

        SetAssistantStatus("Listening...")
        start_time = time.time()  # Track timing for optimization
        Query = SpeechRecognition()
        speech_time = time.time() - start_time
        print(f"⏱️ Speech recognition took: {speech_time:.2f} seconds")

        # Check if speech recognition failed
        if Query in ["I didn't hear anything. Please try again.",
                    "I'm having trouble with speech recognition. Please try again later.",
                    "I didn't catch that.",
                    "Error in speech recognition"]:
            ShowTextToScreen(f"{Assistantname} : {Query}")
            SetAssistantStatus("Answering.....")
            text_to_speech(Query)
            return False

        ShowTextToScreen(f" {Username} : {Query}")
        SetAssistantStatus("Thinking.....")

        # Track AI processing time
        ai_start_time = time.time()
        try:
            Decision = FirstLayerDMM(Query)
            ai_time = time.time() - ai_start_time
            print(f"⏱️ AI processing took: {ai_time:.2f} seconds")
            print("")
            print(f"Decision {Decision}")
            print("")
        except Exception as e:
            print(f"Error in decision making: {e}")
            Answer = "I'm having trouble processing your request. Please try again."
            ShowTextToScreen(f"{Assistantname} : {Answer}")
            SetAssistantStatus("Answering.....")
            text_to_speech(Answer)
            return False

        G = any([i for i in Decision if i.startswith("general")])
        R = any([i for i in Decision if i.startswith("realtime")])

        Mearged_query = " and ".join(
            [" ".join(i.split()[1:]) for i in Decision if i.startswith("general") or i.startswith("realtime")]
        )

        for queries in Decision:
            if TaskExecution == False:
                if queries.startswith("datetime"):
                    # Handle datetime queries
                    SetAssistantStatus("Processing...")
                    datetime_query = queries.replace("datetime ", "")
                    datetime_response = process_datetime_query(datetime_query)
                    ShowTextToScreen(f"{Assistantname} : {datetime_response}")
                    SetAssistantStatus("Answering.....")
                    text_to_speech(datetime_response)
                    return True  # Return immediately after handling datetime query

                elif queries.startswith("send email"):
                    # Handle email commands specially
                    from Backend.Automation import extract_email_from_text, validate_email, SendEmail

                    email_command = queries.replace("send email ", "").strip()

                    if not email_command:
                        # No email address provided, ask for it
                        SetAssistantStatus("Asking for email...")
                        ask_message = "What email address would you like to send to?"
                        ShowTextToScreen(f"{Assistantname} : {ask_message}")
                        text_to_speech(ask_message)

                        # Wait for user response
                        SetAssistantStatus("Listening for email...")
                        email_response = SpeechRecognition()

                        if email_response not in ["I didn't hear anything. Please try again.",
                                                "I'm having trouble with speech recognition. Please try again later.",
                                                "I didn't catch that.",
                                                "Error in speech recognition"]:
                            ShowTextToScreen(f"{Username} : {email_response}")
                            # Extract email from the response
                            email_address = extract_email_from_text(email_response)

                            if email_address and validate_email(email_address):
                                # Valid email found, now ask for subject and content
                                SetAssistantStatus("Asking for subject...")
                                subject_message = "What is the subject or topic of your email?"
                                ShowTextToScreen(f"{Assistantname} : {subject_message}")
                                text_to_speech(subject_message)

                                # Wait for subject response
                                SetAssistantStatus("Listening for subject...")
                                subject_response = SpeechRecognition()

                                if subject_response not in ["I didn't hear anything. Please try again.",
                                                          "I'm having trouble with speech recognition. Please try again later.",
                                                          "I didn't catch that.",
                                                          "Error in speech recognition"]:
                                    ShowTextToScreen(f"{Username} : {subject_response}")

                                    # Ask for email content
                                    SetAssistantStatus("Asking for content...")
                                    content_message = "What would you like to write in the email?"
                                    ShowTextToScreen(f"{Assistantname} : {content_message}")
                                    text_to_speech(content_message)

                                    # Wait for content response
                                    SetAssistantStatus("Listening for content...")
                                    content_response = SpeechRecognition()

                                    if content_response not in ["I didn't hear anything. Please try again.",
                                                              "I'm having trouble with speech recognition. Please try again later.",
                                                              "I didn't catch that.",
                                                              "Error in speech recognition"]:
                                        ShowTextToScreen(f"{Username} : {content_response}")

                                        # Now open Gmail with all information
                                        SetAssistantStatus("Opening Gmail...")
                                        success, message = SendEmail(email_address, subject_response, content_response)
                                        ShowTextToScreen(f"{Assistantname} : {message}")
                                        text_to_speech(message)
                                    else:
                                        # Content speech recognition failed
                                        ShowTextToScreen(f"{Assistantname} : {content_response}")
                                        SetAssistantStatus("Error...")
                                        text_to_speech(content_response)
                                else:
                                    # Subject speech recognition failed
                                    ShowTextToScreen(f"{Assistantname} : {subject_response}")
                                    SetAssistantStatus("Error...")
                                    text_to_speech(subject_response)
                            else:
                                # Invalid or no email found
                                error_message = "I couldn't find a valid email address. Please try again with a proper email <NAME_EMAIL>"
                                ShowTextToScreen(f"{Assistantname} : {error_message}")
                                SetAssistantStatus("Error...")
                                text_to_speech(error_message)
                        else:
                            # Speech recognition failed
                            ShowTextToScreen(f"{Assistantname} : {email_response}")
                            SetAssistantStatus("Error...")
                            text_to_speech(email_response)
                    else:
                        # Email address might be provided in the command
                        email_address = extract_email_from_text(email_command)

                        if email_address and validate_email(email_address):
                            # Valid email found, now ask for subject and content
                            SetAssistantStatus("Asking for subject...")
                            subject_message = "What is the subject or topic of your email?"
                            ShowTextToScreen(f"{Assistantname} : {subject_message}")
                            text_to_speech(subject_message)

                            # Wait for subject response
                            SetAssistantStatus("Listening for subject...")
                            subject_response = SpeechRecognition()

                            if subject_response not in ["I didn't hear anything. Please try again.",
                                                      "I'm having trouble with speech recognition. Please try again later.",
                                                      "I didn't catch that.",
                                                      "Error in speech recognition"]:
                                ShowTextToScreen(f"{Username} : {subject_response}")

                                # Ask for email content
                                SetAssistantStatus("Asking for content...")
                                content_message = "What would you like to write in the email?"
                                ShowTextToScreen(f"{Assistantname} : {content_message}")
                                text_to_speech(content_message)

                                # Wait for content response
                                SetAssistantStatus("Listening for content...")
                                content_response = SpeechRecognition()

                                if content_response not in ["I didn't hear anything. Please try again.",
                                                          "I'm having trouble with speech recognition. Please try again later.",
                                                          "I didn't catch that.",
                                                          "Error in speech recognition"]:
                                    ShowTextToScreen(f"{Username} : {content_response}")

                                    # Now open Gmail with all information
                                    SetAssistantStatus("Opening Gmail...")
                                    success, message = SendEmail(email_address, subject_response, content_response)
                                    ShowTextToScreen(f"{Assistantname} : {message}")
                                    text_to_speech(message)
                                else:
                                    # Content speech recognition failed
                                    ShowTextToScreen(f"{Assistantname} : {content_response}")
                                    SetAssistantStatus("Error...")
                                    text_to_speech(content_response)
                            else:
                                # Subject speech recognition failed
                                ShowTextToScreen(f"{Assistantname} : {subject_response}")
                                SetAssistantStatus("Error...")
                                text_to_speech(subject_response)
                        else:
                            # No valid email in command, ask for it
                            SetAssistantStatus("Asking for email...")
                            ask_message = "What email address would you like to send to?"
                            ShowTextToScreen(f"{Assistantname} : {ask_message}")
                            text_to_speech(ask_message)

                            # Wait for user response
                            SetAssistantStatus("Listening for email...")
                            email_response = SpeechRecognition()

                            if email_response not in ["I didn't hear anything. Please try again.",
                                                    "I'm having trouble with speech recognition. Please try again later.",
                                                    "I didn't catch that.",
                                                    "Error in speech recognition"]:
                                ShowTextToScreen(f"{Username} : {email_response}")
                                # Extract email from the response
                                email_address = extract_email_from_text(email_response)

                                if email_address and validate_email(email_address):
                                    # Valid email found, now ask for subject and content
                                    SetAssistantStatus("Asking for subject...")
                                    subject_message = "What is the subject or topic of your email?"
                                    ShowTextToScreen(f"{Assistantname} : {subject_message}")
                                    text_to_speech(subject_message)

                                    # Wait for subject response
                                    SetAssistantStatus("Listening for subject...")
                                    subject_response = SpeechRecognition()

                                    if subject_response not in ["I didn't hear anything. Please try again.",
                                                              "I'm having trouble with speech recognition. Please try again later.",
                                                              "I didn't catch that.",
                                                              "Error in speech recognition"]:
                                        ShowTextToScreen(f"{Username} : {subject_response}")

                                        # Ask for email content
                                        SetAssistantStatus("Asking for content...")
                                        content_message = "What would you like to write in the email?"
                                        ShowTextToScreen(f"{Assistantname} : {content_message}")
                                        text_to_speech(content_message)

                                        # Wait for content response
                                        SetAssistantStatus("Listening for content...")
                                        content_response = SpeechRecognition()

                                        if content_response not in ["I didn't hear anything. Please try again.",
                                                                  "I'm having trouble with speech recognition. Please try again later.",
                                                                  "I didn't catch that.",
                                                                  "Error in speech recognition"]:
                                            ShowTextToScreen(f"{Username} : {content_response}")

                                            # Now open Gmail with all information
                                            SetAssistantStatus("Opening Gmail...")
                                            success, message = SendEmail(email_address, subject_response, content_response)
                                            ShowTextToScreen(f"{Assistantname} : {message}")
                                            text_to_speech(message)
                                        else:
                                            # Content speech recognition failed
                                            ShowTextToScreen(f"{Assistantname} : {content_response}")
                                            SetAssistantStatus("Error...")
                                            text_to_speech(content_response)
                                    else:
                                        # Subject speech recognition failed
                                        ShowTextToScreen(f"{Assistantname} : {subject_response}")
                                        SetAssistantStatus("Error...")
                                        text_to_speech(subject_response)
                                else:
                                    # Invalid or no email found
                                    error_message = "I couldn't find a valid email address. Please try again with a proper email <NAME_EMAIL>"
                                    ShowTextToScreen(f"{Assistantname} : {error_message}")
                                    SetAssistantStatus("Error...")
                                    text_to_speech(error_message)
                            else:
                                # Speech recognition failed
                                ShowTextToScreen(f"{Assistantname} : {email_response}")
                                SetAssistantStatus("Error...")
                                text_to_speech(email_response)

                    TaskExecution = True

                elif any(queries.startswith("todo") for queries in Decision if isinstance(queries, str)):
                    # Handle TO-DO commands
                    SetAssistantStatus("Processing TO-DO...")
                    todo_response = process_todo_command(queries)
                    ShowTextToScreen(f"{Assistantname} : {todo_response}")
                    SetAssistantStatus("Answering.....")
                    text_to_speech(todo_response)
                    TaskExecution = True

                elif any(queries.startswith("reminder") for queries in Decision if isinstance(queries, str)):
                    # Handle legacy reminder commands - convert to TO-DO format
                    SetAssistantStatus("Processing reminder...")
                    # Convert "reminder in 2 minutes meeting" to "todo add meeting deadline in 2 minutes"
                    reminder_text = queries.replace("reminder ", "")
                    todo_command = f"todo add {reminder_text}"
                    todo_response = process_todo_command(todo_command)
                    ShowTextToScreen(f"{Assistantname} : {todo_response}")
                    SetAssistantStatus("Answering.....")
                    text_to_speech(todo_response)
                    TaskExecution = True

                elif any(queries.startswith(func) for func in Functions if func not in ["datetime", "send email", "todo add", "todo list", "todo remove", "todo edit", "todo complete", "todo show"]):
                    # Run automation and get the result
                    automation_result = run(Automation(list(Decision)))

                    # Check if the result is a string (speech response)
                    if isinstance(automation_result, str):
                        ShowTextToScreen(f"{Assistantname} : {automation_result}")
                        SetAssistantStatus("Opening...")
                        text_to_speech(automation_result)

                    TaskExecution = True

            # Add image generation detection - The AI can generate images using the "generate image [prompt]" command
            if queries.startswith("generate image"):
                ImageExecution = True
                ImageGenerationQuery = queries.replace("generate image ", "")

        if ImageExecution == True:
            # Provide feedback to the user that image generation has started
            image_response = f"I'm generating an image of {ImageGenerationQuery} for you. You can continue giving me other commands while I work on this."
            ShowTextToScreen(f"{Assistantname} : {image_response}")
            SetAssistantStatus("Generating Image...")
            text_to_speech(image_response)

            # Write the image generation request to the data file
            image_gen_path = os.path.join("Frontend", "Files", "ImageGeneration.data")
            with open(image_gen_path, "w") as file:
                file.write(f"{ImageGenerationQuery}, True")

            # Launch the image generation process as a background task
            try:
                backend_script = os.path.join("Backend", "ImageGeneration.py")
                p1 = subprocess.Popen(['python', backend_script],
                                        stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                        stdin=subprocess.PIPE, shell=False)
                subprocess_list.append(p1)
                print(f"Image generation process started for: {ImageGenerationQuery}")
            except Exception as e:
                print(f"Error starting ImageGeneration.py: {e}")
                error_msg = "I encountered an error while trying to generate the images. Please try again."
                ShowTextToScreen(f"{Assistantname} : {error_msg}")
                SetAssistantStatus("Error")
                text_to_speech(error_msg)

        if G and R or R:
            SetAssistantStatus("Searching.....")
            Answer = RealtimeSearchEngine(QueryModifier(Mearged_query))

            # Enhance answer with emotion context
            if EMOTION_ANALYSIS_AVAILABLE:
                emotion_context = get_emotion_context()
                Enhanced_Answer = enhance_response_with_emotion(Answer)
                log_emotion_interaction(Query, Enhanced_Answer, emotion_context)
                Answer = Enhanced_Answer

            ShowTextToScreen(f"{Assistantname} : {Answer}")
            SetAssistantStatus("Answering.....")
            text_to_speech(Answer)
            return True

        else:
            for Queries in Decision:
                if "general" in Queries:
                    SetAssistantStatus("Thinking.....")
                    QueryFinal = Queries.replace("general ", "")
                    Answer = ChatBot(QueryModifier(QueryFinal))

                    # Enhance answer with emotion context
                    if EMOTION_ANALYSIS_AVAILABLE:
                        emotion_context = get_emotion_context()
                        Enhanced_Answer = enhance_response_with_emotion(Answer)
                        log_emotion_interaction(Query, Enhanced_Answer, emotion_context)
                        Answer = Enhanced_Answer

                    ShowTextToScreen(f"{Assistantname} : {Answer}")
                    SetAssistantStatus("Answering.....")
                    text_to_speech(Answer)
                    return True
                elif "realtime" in Queries:
                    SetAssistantStatus("Searching.....")
                    QueryFinal = Queries.replace("realtime ", "")
                    Answer = RealtimeSearchEngine(QueryModifier(QueryFinal))

                    # Enhance answer with emotion context
                    if EMOTION_ANALYSIS_AVAILABLE:
                        emotion_context = get_emotion_context()
                        Enhanced_Answer = enhance_response_with_emotion(Answer)
                        log_emotion_interaction(Query, Enhanced_Answer, emotion_context)
                        Answer = Enhanced_Answer

                    ShowTextToScreen(f"{Assistantname} : {Answer}")
                    SetAssistantStatus("Answering .....")
                    text_to_speech(Answer)
                    return True
                elif "exit" in Queries:
                    QueryFinal = "Okay, Bye!"
                    Answer = ChatBot(QueryModifier(QueryFinal))
                    ShowTextToScreen(f"{Assistantname} : {Answer}")
                    SetAssistantStatus("Answering.....")
                    text_to_speech(Answer)
                    SetAssistantStatus("Answering.....")
                    os._exit(1)

def SpeechRecognitionThread():
    """PRIORITY THREAD: Start speech recognition immediately!"""
    print("🎤 STARTING SPEECH RECOGNITION THREAD - HIGHEST PRIORITY!")

    # Initialize speech recognition FIRST - before anything else
    InitialSpeechSetup()

    # Pre-initialize speech recognition WebDriver for faster first response
    print("🌐 Pre-initializing speech recognition WebDriver...")
    try:
        # Import and trigger WebDriver initialization
        from Backend.SpeechToText import get_webdriver
        get_webdriver()
        print("✅ Speech recognition WebDriver pre-initialized successfully!")
    except Exception as e:
        print(f"⚠️ WebDriver pre-initialization failed (will initialize on first use): {e}")

    print("🚀 Starting main speech recognition loop...")

    # Main speech recognition loop - this runs continuously
    while True:
        CurrentStatus = GetMicrophoneStatus()
        if CurrentStatus.lower() == "true":
            MainExecution()
        else:
            AIStatus = GetAssistantStatus()
            if "Available....." in AIStatus:
                sleep(0.1)
            else:
                SetAssistantStatus("Available.....")

def FirstThread():
    """Secondary thread: Handle GUI coordination and welcome message"""
    # Wait for GUI to be ready and loading screen to complete
    print("🚀 Waiting for GUI initialization...")

    # Wait for GUI to signal it's ready
    while not is_gui_ready():
        sleep(0.1)

    print("🎬 GUI ready, waiting for loading screen to complete...")

    # Wait for loading screen to complete (60 seconds)
    while not is_loading_screen_complete():
        sleep(0.5)  # Check every 500ms

    print("✅ Loading screen completed! Preparing welcome message...")

    # Small delay to ensure GUI is fully visible before speaking
    sleep(2)

    # Run the initial execution (this will speak the welcome message)
    # Speech recognition is already active from SpeechRecognitionThread
    print("🎤 Running initial execution...")
    InitialExecution()

def run_gta_loading_screen():
    """Run the GTA IV-style loading screen and wait for completion"""
    try:
        print("🎬 Starting GTA IV-style Loading Screen...")
        print("⏱️  Duration: 60 seconds exactly")
        print("🎵 Audio: Automatic detection")
        print("💡 Tips: Application features rotation")
        print("🖥️  Main GUI window will remain hidden until loading completes")

        import subprocess
        import sys

        # Run the loading screen and wait for it to complete (60 seconds + buffer)
        result = subprocess.run([sys.executable, "matrix_startup_animation.py"],
                              capture_output=False, text=True, timeout=75)

        if result.returncode == 0:
            print("✅ GTA IV Loading Screen completed successfully!")
            # Signal that loading screen is complete
            signal_loading_screen_complete()
            return True
        else:
            print(f"⚠️ Loading screen exited with code: {result.returncode}")
            # Even if exit code is non-zero, signal completion to show GUI
            signal_loading_screen_complete()
            return True  # Continue anyway

    except subprocess.TimeoutExpired:
        print("⏰ Loading screen timed out - proceeding to main application")
        # Signal completion even on timeout
        signal_loading_screen_complete()
        return True  # Continue anyway
    except FileNotFoundError:
        print("❌ matrix_startup_animation.py not found!")
        print("⚠️ Skipping loading screen - starting main application directly")
        # Signal completion to show GUI immediately
        signal_loading_screen_complete()
        return False
    except Exception as e:
        print(f"❌ Error running loading screen: {e}")
        print("🔄 Proceeding to main application anyway...")
        # Signal completion even on error
        signal_loading_screen_complete()
        return False

def SecondThread():
    """Start the GUI hidden, then show it after startup completion"""
    # Give startup animation time to initialize
    sleep(1)
    print("🖥️  Starting Matrix AI GUI (hidden)...")

    # Start GUI in hidden mode
    GraphicalUserInterface()

def ThirdThread():
    """Run the GTA IV loading screen"""
    run_gta_loading_screen()

def ensure_single_instance():
    """Ensure only one instance of the application is running"""
    # Create a unique lock file path
    lock_file = os.path.join(tempfile.gettempdir(), 'matrix_ai_instance.lock')

    try:
        # Try to create the lock file
        if os.path.exists(lock_file):
            # Check if the process is still running
            try:
                with open(lock_file, 'r') as f:
                    pid = int(f.read().strip())
                # Try to check if process exists (platform-dependent)
                try:
                    os.kill(pid, 0)  # This doesn't kill the process, just checks if it exists
                    print(f"⚠️ Another instance is already running (PID: {pid})")
                    print("⚠️ Exiting to prevent conflicts...")
                    sys.exit(1)
                except OSError:
                    # Process doesn't exist, we can take over the lock
                    print("🔄 Found stale lock file from crashed instance, taking over")
                    pass
            except (ValueError, IOError):
                # Invalid PID in file or couldn't read file
                pass

        # Create our lock file
        with open(lock_file, 'w') as f:
            f.write(str(os.getpid()))

        # Register to remove the lock file on clean exit
        def cleanup_lock():
            try:
                if os.path.exists(lock_file):
                    os.remove(lock_file)
            except:
                pass

        atexit.register(cleanup_lock)
        return True

    except Exception as e:
        print(f"⚠️ Error in single instance check: {e}")
        # Continue anyway to prevent blocking startup
        return True

if __name__ == "__main__":
    print("=" * 60)
    print("🤖 MATRIX AI - SPECTACULAR STARTUP SEQUENCE")
    print("🎤 SPEECH RECOGNITION STARTS FIRST!")
    print("🎬 LOADING SCREEN RUNS FOR 60 SECONDS")
    print("🖥️  GUI APPEARS ONLY AFTER LOADING COMPLETES")
    print("=" * 60)

    # Clean up any leftover startup files from previous runs
    cleanup_startup_files()

    # Ensure we're the only instance running
    if not ensure_single_instance():
        sys.exit(1)

    # PRIORITY 1: Start speech recognition thread IMMEDIATELY
    print("🚀 Starting speech recognition thread (HIGHEST PRIORITY)...")
    speech_thread = threading.Thread(target=SpeechRecognitionThread, daemon=True)
    speech_thread.start()

    # Start emotion bridge for real-time emotion display
    if EMOTION_ANALYSIS_AVAILABLE:
        print("🎭 Starting emotion bridge for real-time GUI updates...")
        start_emotion_bridge()

    # Small delay to ensure speech recognition initializes
    sleep(1)

    # PRIORITY 2: Start the spectacular startup animation (60-second loading screen)
    print("🎬 Starting 60-second loading screen...")
    startup_thread = threading.Thread(target=ThirdThread, daemon=True)
    startup_thread.start()

    # PRIORITY 3: Start the GUI coordination thread (waits for loading completion)
    print("🖥️ Starting GUI coordination thread...")
    main_thread = threading.Thread(target=FirstThread, daemon=True)
    main_thread.start()

    # PRIORITY 4: Start the GUI (hidden until loading completes)
    print("🖼️ Starting GUI interface (hidden during loading)...")
    try:
        SecondThread()
    finally:
        # Clean up emotion bridge
        if EMOTION_ANALYSIS_AVAILABLE:
            print("🎭 Stopping emotion bridge...")
            stop_emotion_bridge()

        # Clean up TTS resources
        print("🔊 Cleaning up TTS resources...")
        cleanup_tts()

        # Clean up startup files when application exits
        print("🧹 Cleaning up startup coordination files...")
        cleanup_startup_files()

    print("🎯 Matrix AI Session Complete!")
